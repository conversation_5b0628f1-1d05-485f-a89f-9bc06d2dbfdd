<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;
use App\Models\Section;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data = Product::with('section')->get();
        return view('admin.products.index', ['data' => $data]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $sections = Section::all();
        return view('admin.products.create', ['sections' => $sections]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'proname' => 'required|string|max:255',
                'proprice' => 'required|numeric',
                'proimg' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'section_id' => 'required|exists:sections,id',
            ]);

            $imagePath = null;

            // Handle image upload
            if ($request->hasFile('proimg')) {
                $image = $request->file('proimg');
                $imageName = time() . '_' . $image->getClientOriginalName();

                // Create uploads directory if it doesn't exist
                $uploadPath = public_path('uploads/products');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                // Move the uploaded file
                $image->move($uploadPath, $imageName);
                $imagePath = 'uploads/products/' . $imageName;
            }

            Product::create([
                'proname' => $request->proname,
                'proimg' => $imagePath,
                'proprice' => $request->proprice,
                'section_id' => $request->section_id,
                'prodescription' => $request->prodescription,
                'prosize' => $request->prosize,
                'prounv' => $request->prounv,
            ]);

            return redirect()->route('products.index')->with('success', __('app.products_created_successfully'));
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'حدث خطأ أثناء الحفظ: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $sections = Section::all();
        return view('admin.products.create', compact('product', 'sections'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        try {
            $request->validate([
                'proname' => 'required|string|max:255',
                'proprice' => 'required|numeric',
                'proimg' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'section_id' => 'required|exists:sections,id',
            ]);

            $imagePath = $product->proimg; // Keep existing image path

            // Handle image upload
            if ($request->hasFile('proimg')) {
                // Delete old image if exists
                if ($product->proimg && file_exists(public_path($product->proimg))) {
                    unlink(public_path($product->proimg));
                }

                $image = $request->file('proimg');
                $imageName = time() . '_' . $image->getClientOriginalName();

                // Create uploads directory if it doesn't exist
                $uploadPath = public_path('uploads/products');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                // Move the uploaded file
                $image->move($uploadPath, $imageName);
                $imagePath = "uploads/products/{$imageName}";
            }

            $product->update([
                'proname' => $request->proname,
                'proimg' => $imagePath,
                'proprice' => $request->proprice,
                'section_id' => $request->section_id,
                'prodescription' => $request->prodescription,
                'prosize' => $request->prosize,
                'prounv' => $request->prounv,
            ]);

            return redirect()->route('products.index')->with('success', __('app.products_updated_successfully'));
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'حدث خطأ أثناء التحديث: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        try {
            // Delete associated image if exists
            if ($product->proimg && file_exists(public_path($product->proimg))) {
                unlink(public_path($product->proimg));
            }

            $product->delete();

            return redirect()->route('products.index')->with('success', __('app.product_deleted_successfully'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ أثناء الحذف: ' . $e->getMessage());
        }
    }
}
