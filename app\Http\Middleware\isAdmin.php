<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class isAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // dd(Auth::check());
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect('/login');
        }

        // // Get the authenticated user
        $user = Auth::user();

        // // Check if user exists and is admin
        if (!$user || !$user->is_admin) {
            return redirect('/welcome')->with('error', 'Access denied. Admin privileges required.');
        }

        return $next($request);
    }
}
