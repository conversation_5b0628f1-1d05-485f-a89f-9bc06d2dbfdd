@font-face {
  font-family: "simple-line-icons";
  src: url("https://eduadmin-template.multipurposethemes.com/bs5/assets/icons/simple-line-icons/fonts/Simple-Line-Icons.eot?v=2.4.0");
  src: url("https://eduadmin-template.multipurposethemes.com/bs5/assets/icons/simple-line-icons/fonts/Simple-Line-Icons.eot?v=2.4.0#iefix") format("embedded-opentype"), url("https://eduadmin-template.multipurposethemes.com/bs5/assets/icons/simple-line-icons/fonts/Simple-Line-Icons.woff2?v=2.4.0") format("woff2"), url("https://eduadmin-template.multipurposethemes.com/bs5/assets/icons/simple-line-icons/fonts/Simple-Line-Icons.ttf?v=2.4.0") format("truetype"), url("https://eduadmin-template.multipurposethemes.com/bs5/assets/icons/simple-line-icons/fonts/Simple-Line-Icons.woff?v=2.4.0") format("woff"), url("https://eduadmin-template.multipurposethemes.com/bs5/assets/icons/simple-line-icons/fonts/Simple-Line-Icons.svg?v=2.4.0#simple-line-icons") format("svg");
  font-weight: normal;
  font-style: normal;
}

.si {
  font-family: "simple-line-icons";
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.si-user:before {
  content: "\e005";
}

.si-people:before {
  content: "\e001";
}

.si-user-female:before {
  content: "\e000";
}

.si-user-follow:before {
  content: "\e002";
}

.si-user-following:before {
  content: "\e003";
}

.si-user-unfollow:before {
  content: "\e004";
}

.si-login:before {
  content: "\e066";
}

.si-logout:before {
  content: "\e065";
}

.si-emotsmile:before {
  content: "\e021";
}

.si-phone:before {
  content: "\e600";
}

.si-call-end:before {
  content: "\e048";
}

.si-call-in:before {
  content: "\e047";
}

.si-call-out:before {
  content: "\e046";
}

.si-map:before {
  content: "\e033";
}

.si-location-pin:before {
  content: "\e096";
}

.si-direction:before {
  content: "\e042";
}

.si-directions:before {
  content: "\e041";
}

.si-compass:before {
  content: "\e045";
}

.si-layers:before {
  content: "\e034";
}

.si-menu:before {
  content: "\e601";
}

.si-list:before {
  content: "\e067";
}

.si-options-vertical:before {
  content: "\e602";
}

.si-options:before {
  content: "\e603";
}

.si-arrow-down:before {
  content: "\e604";
}

.si-arrow-left:before {
  content: "\e605";
}

.si-arrow-right:before {
  content: "\e606";
}

.si-arrow-up:before {
  content: "\e607";
}

.si-arrow-up-circle:before {
  content: "\e078";
}

.si-arrow-left-circle:before {
  content: "\e07a";
}

.si-arrow-right-circle:before {
  content: "\e079";
}

.si-arrow-down-circle:before {
  content: "\e07b";
}

.si-check:before {
  content: "\e080";
}

.si-clock:before {
  content: "\e081";
}

.si-plus:before {
  content: "\e095";
}

.si-minus:before {
  content: "\e615";
}

.si-close:before {
  content: "\e082";
}

.si-event:before {
  content: "\e619";
}

.si-exclamation:before {
  content: "\e617";
}

.si-organization:before {
  content: "\e616";
}

.si-trophy:before {
  content: "\e006";
}

.si-screen-smartphone:before {
  content: "\e010";
}

.si-screen-desktop:before {
  content: "\e011";
}

.si-plane:before {
  content: "\e012";
}

.si-notebook:before {
  content: "\e013";
}

.si-mustache:before {
  content: "\e014";
}

.si-mouse:before {
  content: "\e015";
}

.si-magnet:before {
  content: "\e016";
}

.si-energy:before {
  content: "\e020";
}

.si-disc:before {
  content: "\e022";
}

.si-cursor:before {
  content: "\e06e";
}

.si-cursor-move:before {
  content: "\e023";
}

.si-crop:before {
  content: "\e024";
}

.si-chemistry:before {
  content: "\e026";
}

.si-speedometer:before {
  content: "\e007";
}

.si-shield:before {
  content: "\e00e";
}

.si-screen-tablet:before {
  content: "\e00f";
}

.si-magic-wand:before {
  content: "\e017";
}

.si-hourglass:before {
  content: "\e018";
}

.si-graduation:before {
  content: "\e019";
}

.si-ghost:before {
  content: "\e01a";
}

.si-game-controller:before {
  content: "\e01b";
}

.si-fire:before {
  content: "\e01c";
}

.si-eyeglass:before {
  content: "\e01d";
}

.si-envelope-open:before {
  content: "\e01e";
}

.si-envelope-letter:before {
  content: "\e01f";
}

.si-bell:before {
  content: "\e027";
}

.si-badge:before {
  content: "\e028";
}

.si-anchor:before {
  content: "\e029";
}

.si-wallet:before {
  content: "\e02a";
}

.si-vector:before {
  content: "\e02b";
}

.si-speech:before {
  content: "\e02c";
}

.si-puzzle:before {
  content: "\e02d";
}

.si-printer:before {
  content: "\e02e";
}

.si-present:before {
  content: "\e02f";
}

.si-playlist:before {
  content: "\e030";
}

.si-pin:before {
  content: "\e031";
}

.si-picture:before {
  content: "\e032";
}

.si-handbag:before {
  content: "\e035";
}

.si-globe-alt:before {
  content: "\e036";
}

.si-globe:before {
  content: "\e037";
}

.si-folder-alt:before {
  content: "\e039";
}

.si-folder:before {
  content: "\e089";
}

.si-film:before {
  content: "\e03a";
}

.si-feed:before {
  content: "\e03b";
}

.si-drop:before {
  content: "\e03e";
}

.si-drawer:before {
  content: "\e03f";
}

.si-docs:before {
  content: "\e040";
}

.si-doc:before {
  content: "\e085";
}

.si-diamond:before {
  content: "\e043";
}

.si-cup:before {
  content: "\e044";
}

.si-calculator:before {
  content: "\e049";
}

.si-bubbles:before {
  content: "\e04a";
}

.si-briefcase:before {
  content: "\e04b";
}

.si-book-open:before {
  content: "\e04c";
}

.si-basket-loaded:before {
  content: "\e04d";
}

.si-basket:before {
  content: "\e04e";
}

.si-bag:before {
  content: "\e04f";
}

.si-action-undo:before {
  content: "\e050";
}

.si-action-redo:before {
  content: "\e051";
}

.si-wrench:before {
  content: "\e052";
}

.si-umbrella:before {
  content: "\e053";
}

.si-trash:before {
  content: "\e054";
}

.si-tag:before {
  content: "\e055";
}

.si-support:before {
  content: "\e056";
}

.si-frame:before {
  content: "\e038";
}

.si-size-fullscreen:before {
  content: "\e057";
}

.si-size-actual:before {
  content: "\e058";
}

.si-shuffle:before {
  content: "\e059";
}

.si-share-alt:before {
  content: "\e05a";
}

.si-share:before {
  content: "\e05b";
}

.si-rocket:before {
  content: "\e05c";
}

.si-question:before {
  content: "\e05d";
}

.si-pie-chart:before {
  content: "\e05e";
}

.si-pencil:before {
  content: "\e05f";
}

.si-note:before {
  content: "\e060";
}

.si-loop:before {
  content: "\e064";
}

.si-home:before {
  content: "\e069";
}

.si-grid:before {
  content: "\e06a";
}

.si-graph:before {
  content: "\e06b";
}

.si-microphone:before {
  content: "\e063";
}

.si-music-tone-alt:before {
  content: "\e061";
}

.si-music-tone:before {
  content: "\e062";
}

.si-earphones-alt:before {
  content: "\e03c";
}

.si-earphones:before {
  content: "\e03d";
}

.si-equalizer:before {
  content: "\e06c";
}

.si-like:before {
  content: "\e068";
}

.si-dislike:before {
  content: "\e06d";
}

.si-control-start:before {
  content: "\e06f";
}

.si-control-rewind:before {
  content: "\e070";
}

.si-control-play:before {
  content: "\e071";
}

.si-control-pause:before {
  content: "\e072";
}

.si-control-forward:before {
  content: "\e073";
}

.si-control-end:before {
  content: "\e074";
}

.si-volume-1:before {
  content: "\e09f";
}

.si-volume-2:before {
  content: "\e0a0";
}

.si-volume-off:before {
  content: "\e0a1";
}

.si-calendar:before {
  content: "\e075";
}

.si-bulb:before {
  content: "\e076";
}

.si-chart:before {
  content: "\e077";
}

.si-ban:before {
  content: "\e07c";
}

.si-bubble:before {
  content: "\e07d";
}

.si-camrecorder:before {
  content: "\e07e";
}

.si-camera:before {
  content: "\e07f";
}

.si-cloud-download:before {
  content: "\e083";
}

.si-cloud-upload:before {
  content: "\e084";
}

.si-envelope:before {
  content: "\e086";
}

.si-eye:before {
  content: "\e087";
}

.si-flag:before {
  content: "\e088";
}

.si-heart:before {
  content: "\e08a";
}

.si-info:before {
  content: "\e08b";
}

.si-key:before {
  content: "\e08c";
}

.si-link:before {
  content: "\e08d";
}

.si-lock:before {
  content: "\e08e";
}

.si-lock-open:before {
  content: "\e08f";
}

.si-magnifier:before {
  content: "\e090";
}

.si-magnifier-add:before {
  content: "\e091";
}

.si-magnifier-remove:before {
  content: "\e092";
}

.si-paper-clip:before {
  content: "\e093";
}

.si-paper-plane:before {
  content: "\e094";
}

.si-power:before {
  content: "\e097";
}

.si-refresh:before {
  content: "\e098";
}

.si-reload:before {
  content: "\e099";
}

.si-settings:before {
  content: "\e09a";
}

.si-star:before {
  content: "\e09b";
}

.si-symbol-female:before {
  content: "\e09c";
}

.si-symbol-male:before {
  content: "\e09d";
}

.si-target:before {
  content: "\e09e";
}

.si-credit-card:before {
  content: "\e025";
}

.si-paypal:before {
  content: "\e608";
}

.si-social-tumblr:before {
  content: "\e00a";
}

.si-social-twitter:before {
  content: "\e009";
}

.si-social-facebook:before {
  content: "\e00b";
}

.si-social-instagram:before {
  content: "\e609";
}

.si-social-linkedin:before {
  content: "\e60a";
}

.si-social-pinterest:before {
  content: "\e60b";
}

.si-social-github:before {
  content: "\e60c";
}

.si-social-google:before {
  content: "\e60d";
}

.si-social-reddit:before {
  content: "\e60e";
}

.si-social-skype:before {
  content: "\e60f";
}

.si-social-dribbble:before {
  content: "\e00d";
}

.si-social-behance:before {
  content: "\e610";
}

.si-social-foursqare:before {
  content: "\e611";
}

.si-social-soundcloud:before {
  content: "\e612";
}

.si-social-spotify:before {
  content: "\e613";
}

.si-social-stumbleupon:before {
  content: "\e614";
}

.si-social-youtube:before {
  content: "\e008";
}

.si-social-dropbox:before {
  content: "\e00c";
}

.si-social-vkontakte:before {
  content: "\e618";
}

.si-social-steam:before {
  content: "\e620";
}

.si-users:before {
  content: "\e001";
}

.si-bar-chart:before {
  content: "\e077";
}

.si-camcorder:before {
  content: "\e07e";
}

.si-emoticon-smile:before {
  content: "\e021";
}

.si-eyeglasses:before {
  content: "\e01d";
}

.si-moustache:before {
  content: "\e014";
}

.si-pointer:before {
  content: "\e096";
}