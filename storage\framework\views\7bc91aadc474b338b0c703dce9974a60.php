<?php echo $__env->make('layouts.admin.head', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<body class="hold-transition light-skin sidebar-mini theme-primary fixed">

    <div class="wrapper">
        


        <?php echo $__env->make('layouts.admin.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php echo $__env->make('layouts.admin.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>


        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper">
            <div class="container-full">
                <?php echo $__env->make('layouts.admin.alert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>


                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
        <!-- /.content-wrapper -->
        <?php echo $__env->make('layouts.admin.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Control Sidebar -->
        <?php echo $__env->make('layouts.admin.sidebar_control', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <!-- /.control-sidebar -->

        <!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
        

    </div>
    <!-- ./wrapper -->

    <!-- ./side demo panel -->
    
    <!-- Sidebar -->

    

    <!-- Page Content overlay -->


    <!-- Vendor JS -->
    <?php echo $__env->make('layouts.admin.footer_script', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH F:\shop\resources\views/layouts/admin/app.blade.php ENDPATH**/ ?>