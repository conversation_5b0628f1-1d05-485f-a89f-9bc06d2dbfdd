<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

Route::get('/welcome', function () {
    // return Auth::check();
    return view('welcome');
});



Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});
Route::middleware(['auth', 'isAdmin'])->group(function () {
    Route::get('/dashboard', function () {
        // return Auth::check();
        return view('admin.index');
    })->middleware('auth')->name('dashboard');
    Route::resource('sections', 'App\Http\Controllers\Admin\sectionController');
    Route::resource('products', 'App\Http\Controllers\Admin\productController');
});

require __DIR__ . '/auth.php';
